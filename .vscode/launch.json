{"version": "0.2.0", "configurations": [{"name": "Debug Next.js Server", "type": "node", "request": "launch", "program": "${workspaceFolder}/frontend/node_modules/next/dist/bin/next", "args": ["dev"], "cwd": "${workspaceFolder}/frontend", "skipFiles": ["<node_internals>/**"], "env": {"NODE_OPTIONS": "--inspect=9229"}, "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "serverReadyAction": {"pattern": "- Local:\\s+([^\\s]+)", "uriFormat": "%s", "action": "openExternally"}}, {"name": "Python: debug backend", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/backend/main.py", "cwd": "${workspaceFolder}/backend", "justMyCode": true, "env": {"UV_VENV": "${workspaceFolder}/backend/.venv"}}]}